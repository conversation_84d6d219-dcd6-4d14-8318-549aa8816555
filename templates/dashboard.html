<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title data-i18n="page.title">控制面板 - ChatGPTPro俱乐部</title>
    <link rel="icon" href="static/images/logo.png"> <!-- Optional: Use your own icon -->
    <link href="https://gcore.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://gcore.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    <style>
        :root {
            --sidebar-width: 260px;
            --topbar-height: 64px;
            /* 现代化渐变配色方案 */
            --primary-color: #667eea; /* 现代蓝紫色 */
            --primary-dark: #5a67d8; /* 深蓝紫色 */
            --accent-color: #764ba2; /* 优雅紫色 */
            --accent-light: #f093fb; /* 浅粉紫色 */
            --highlight-color: #ffecd2; /* 暖金渐变起点 */
            --highlight-end: #fcb69f; /* 暖金渐变终点 */
            --success-color: #48bb78; /* 现代绿色 */
            --warning-color: #ed8936; /* 现代橙色 */
            --danger-color: #f56565; /* 现代红色 */
            --info-color: #4299e1; /* 现代蓝色 */

            /* 背景渐变 */
            --body-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --body-bg-overlay: rgba(255, 255, 255, 0.95);
            --secondary-bg: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --card-bg: rgba(255, 255, 255, 0.95);
            --sidebar-bg: rgba(255, 255, 255, 0.98);
            --sidebar-text: #4a5568;
            --sidebar-active-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --sidebar-active-text: #ffffff;

            /* 现代化阴影系统 */
            --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
            --sidebar-shadow: 0 0 50px rgba(102, 126, 234, 0.15);
            --text-muted: #718096;
            --border-color: rgba(102, 126, 234, 0.1);
        }

        body {
            background: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            font-size: 0.9rem;
            position: relative;
            min-height: 100vh;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--body-bg-overlay);
            z-index: -1;
        }

        .layout-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 1030;
            background: var(--sidebar-bg);
            backdrop-filter: blur(20px);
            box-shadow: var(--sidebar-shadow);
            display: flex;
            flex-direction: column;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-right: 1px solid var(--border-color);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            height: var(--topbar-height);
            border-bottom: 1px solid var(--border-color);
            font-size: 1.2rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            white-space: nowrap;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }
        .sidebar-logo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 12px;
            margin: 8px;
        }
        .sidebar-logo:hover::before {
            opacity: 0.1;
        }
        .sidebar-logo img {
            height: 36px;
            margin-right: 12px;
            filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
        }

        .sidebar-menu {
            flex-grow: 1;
            overflow-y: auto;
            padding-top: 1rem;
        }

        .menu-group .group-title {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-muted);
            text-transform: uppercase;
            margin-top: 0.5rem;
            letter-spacing: 0.5px;
        }

        .menu-group .nav-link {
            display: flex;
            align-items: center;
            padding: 0.875rem 1.5rem;
            color: var(--sidebar-text);
            border-radius: 12px;
            margin: 0 1rem 0.25rem 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            font-weight: 500;
        }
        .menu-group .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 0;
            background: var(--sidebar-active-bg);
            border-radius: 2px;
            transition: height 0.3s ease;
        }
        .menu-group .nav-link:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            color: var(--primary-color);
            transform: translateX(4px);
        }
        .menu-group .nav-link:hover::before {
            height: 20px;
        }
        .menu-group .nav-link.active {
            background: var(--sidebar-active-bg);
            color: var(--sidebar-active-text);
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transform: translateX(4px);
        }
        .menu-group .nav-link.active::before {
            height: 24px;
            background: rgba(255, 255, 255, 0.8);
        }
        .menu-group .nav-link i {
            margin-right: 10px;
            font-size: 1.1rem;
            width: 20px; /* Align icons */
            text-align: center;
        }

        .sidebar-social {
            padding: 0.75rem 1rem;
            border-top: 1px solid rgba(52, 152, 219, 0.1);
        }
        
        .social-header {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-muted);
            text-transform: uppercase;
            margin-bottom: 0.75rem;
            letter-spacing: 0.5px;
            padding: 0 0.5rem;
        }
        
        .social-links {
            display: flex;
            justify-content: space-around;
            gap: 0.5rem;
        }
        
        .social-link {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 0.25rem;
            border-radius: 8px;
            background-color: rgba(52, 152, 219, 0.05);
            color: var(--sidebar-text);
            text-decoration: none;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }
        
        .social-link:hover {
            background-color: rgba(52, 152, 219, 0.15);
            color: var(--accent-color);
            transform: translateY(-1px);
        }
        
        .social-link i {
            font-size: 1.25rem;
            margin-bottom: 0.25rem;
        }
        
        .social-link span {
            font-size: 0.7rem;
            font-weight: 500;
        }

        .sidebar-footer {
            padding: 0.75rem 1rem;
            font-size: 0.8rem;
            color: #6c757d;
            text-align: center;
            border-top: 1px solid rgba(52, 152, 219, 0.1);
        }

        .layout-main {
            margin-left: var(--sidebar-width);
            width: calc(100% - var(--sidebar-width));
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            transition: margin-left 0.3s ease-in-out, width 0.3s ease-in-out;
        }

        .topbar {
            height: var(--topbar-height);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: flex;
            align-items: center;
            padding: 0 2rem;
            z-index: 1020;
            position: sticky;
            top: 0;
            border-bottom: 1px solid var(--border-color);
        }

        .topbar-title {
            font-size: 1.2rem;
            font-weight: 500;
            color: #333;
        }
        
        .topbar-actions {
            margin-left: auto;
            display: flex;
            align-items: center;
        }
        .topbar-actions .nav-item {
            margin-left: 1rem;
        }
        .topbar-actions .dropdown-toggle::after {
             display: none; /* Hide default dropdown arrow */
        }

        .dropdown-menu .dropdown-item:active {
            background-color: var(--accent-color);
        }

        .dropdown-menu .dropdown-item:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }
        
        /* 用户信息下拉样式 */
        .dropdown-header {
            padding: 0.75rem 1rem;
            background-color: rgba(52, 152, 219, 0.05);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        #dropdown-username {
            font-size: 0.95rem;
            color: var(--primary-color);
        }
        #dropdown-email {
            font-size: 0.8rem;
            display: block;
            margin-top: 0.25rem;
        }
        .topbar-actions .avatar {
            width: 32px;
            height: 32px;
            background-color: var(--accent-color);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-weight: 500;
            font-size: 0.9rem;
            cursor: pointer;
        }


        .main-content {
            padding: 1.5rem;
            flex-grow: 1;
        }

        .card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            margin-bottom: 2rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }
        .card:hover {
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-8px);
            border-color: rgba(102, 126, 234, 0.2);
        }
        .card-header {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            border-bottom: 1px solid var(--border-color);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1.1rem;
        }
        .card-header i {
            margin-right: 10px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .overview-card {
            position: relative;
            overflow: hidden;
        }
        .overview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .overview-card:hover::before {
            opacity: 1;
        }
        .overview-card .card-body {
            display: flex;
            align-items: center;
            padding: 1.75rem 1.5rem;
            position: relative;
            z-index: 1;
        }
        .overview-card .icon {
            font-size: 2.5rem;
            margin-right: 1.25rem;
            padding: 1rem;
            border-radius: 16px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }
        .overview-card:hover .icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }
        .overview-card .content .title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .overview-card .content .subtitle {
            font-size: 0.9rem;
            color: var(--text-muted);
            font-weight: 500;
        }

        .subscription-info {
            background-color: #f8f9fa; /* Subtle background */
            border-radius: 8px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #dee2e6;
        }

        .date-countdown {
            font-weight: bold;
            color: var(--accent-color);
        }
        /* 现代化状态指示器 */
        .status-active {
            color: var(--success-color);
            position: relative;
        }
        .status-active::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--success-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        .status-inactive {
            color: var(--danger-color);
            position: relative;
        }
        .status-inactive::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--danger-color);
            border-radius: 50%;
        }
        .status-expired {
            color: var(--warning-color);
            position: relative;
        }
        .status-expired::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--warning-color);
            border-radius: 50%;
            animation: blink 1s infinite;
        }
        .status-success { color: var(--success-color); }
        .status-danger { color: var(--danger-color); }

        @keyframes pulse {
            0% { opacity: 1; transform: translateY(-50%) scale(1); }
            50% { opacity: 0.5; transform: translateY(-50%) scale(1.2); }
            100% { opacity: 1; transform: translateY(-50%) scale(1); }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        /* 现代化进度条 */
        .progress {
            background-color: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .progress-bar {
            background: linear-gradient(135deg, var(--success-color) 0%, #38a169 100%);
            border-radius: 10px;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 现代化警告框 */
        .alert {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
        }
        .alert-info {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
            border-left: 4px solid var(--info-color);
            color: var(--info-color);
        }
        .alert-warning {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(255, 193, 7, 0.1) 100%);
            border-left: 4px solid var(--warning-color);
            color: var(--warning-color);
        }
        .alert-success {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(56, 161, 105, 0.1) 100%);
            border-left: 4px solid var(--success-color);
            color: var(--success-color);
        }

        /* 现代化表格 */
        .table {
            background: var(--card-bg);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }
        .table thead th {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            border-bottom: 2px solid var(--border-color);
            font-weight: 600;
            color: var(--primary-color);
            padding: 1rem;
        }
        .table tbody tr {
            transition: all 0.3s ease;
        }
        .table tbody tr:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
            transform: scale(1.01);
        }

        #pricing-container .card-title.pricing-card-title {
            font-size: 2.5rem;
            font-weight: 300;
        }
        #pricing-container .card-header {
            font-weight: 600;
        }
        
        /* 现代化按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        .btn-primary:hover::before {
            left: 100%;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--accent-color) 100%);
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        /* Direct Login Button specific styling */
        #direct-login-btn {
            background: linear-gradient(135deg, var(--success-color) 0%, #38a169 100%);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
        }
        #direct-login-btn:hover {
            background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
            box-shadow: 0 12px 35px rgba(72, 187, 120, 0.4);
        }

        /* 现代化响应式设计 */
        @media (max-width: 767.98px) {
            .sidebar {
                margin-left: calc(-1 * var(--sidebar-width));
                box-shadow: none;
                z-index: 1040;
            }
            .layout-main {
                width: 100%;
                margin-left: 0;
            }
            .sidebar.show {
                margin-left: 0;
                box-shadow: 0 0 50px rgba(102, 126, 234, 0.3);
            }
            .topbar-toggler {
                display: block !important;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
                color: var(--primary-color);
                border-radius: 8px;
                padding: 0.5rem;
                transition: all 0.3s ease;
            }
            .topbar-toggler:hover {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
                transform: scale(1.05);
            }
            .card:hover {
                transform: translateY(-4px);
            }
            
            /* 移动端表格优化 */
            .table-responsive {
                font-size: 0.875rem;
            }
            .table td, .table th {
                padding: 0.5rem;
            }
            
            /* 移动端按钮优化 */
            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }
            
            /* 移动端卡片优化 */
            .card {
                margin-bottom: 1rem;
            }
            .card-header {
                padding: 0.75rem 1rem;
            }
            .card-body {
                padding: 1rem;
            }
            
            /* 概览卡片移动端优化 */
            .overview-card .card-body {
                padding: 1rem;
            }
            .overview-card .icon {
                font-size: 1.5rem;
                margin-right: 0.75rem;
            }
            
            /* 移动端提示框优化 */
            .alert {
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
            }
            
            /* 移动端模态框优化 */
            .modal-dialog {
                margin: 0.5rem;
            }
            .modal-content {
                border-radius: 0.5rem;
            }
            
            /* 移动端下拉菜单优化 */
            .dropdown-menu {
                font-size: 0.875rem;
            }
            
            /* 移动端侧边栏优化 */
            .sidebar-logo {
                font-size: 1rem;
            }
            .menu-group .nav-link {
                padding: 0.6rem 1rem;
                font-size: 0.875rem;
            }
            .menu-group .group-title {
                font-size: 0.7rem;
            }
            
            /* 移动端社交链接优化 */
            .sidebar-social {
                padding: 0.75rem;
            }
            .social-link {
                padding: 0.4rem 0.2rem;
            }
            .social-link i {
                font-size: 1.1rem;
            }
            .social-link span {
                font-size: 0.65rem;
            }
        }

        /* 现代化加载动画 */
        .spinner-border {
            border-width: 3px;
            border-color: var(--primary-color) transparent var(--primary-color) transparent;
            animation: spin 1s linear infinite, pulse-border 2s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse-border {
            0%, 100% { border-width: 3px; }
            50% { border-width: 4px; }
        }

        /* 现代化加载覆盖层 */
        .loading-overlay {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9999;
        }

        /* 现代化滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--accent-color) 100%);
        }

        /* 现代化输入框 */
        .form-control {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: rgba(255, 255, 255, 1);
        }

        /* 现代化徽章 */
        .badge {
            border-radius: 8px;
            padding: 0.5rem 0.75rem;
            font-weight: 600;
            font-size: 0.75rem;
        }
        .badge-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }
        .badge-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #38a169 100%);
        }
        .badge-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d69e2e 100%);
        }
        .badge-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #e53e3e 100%);
        }

        /* Extra small devices */
        @media (max-width: 575.98px) {
            .main-content {
                padding: 1rem 0.75rem;
            }
            .topbar {
                padding: 0 0.75rem;
            }
            .topbar-title {
                font-size: 1rem;
                flex: 1;
                margin: 0 0.5rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            
            /* 移动端优化：隐藏文字，只显示图标 */
            #current-language {
                display: none;
            }
            #user-display {
                display: none;
            }
            
            /* 缩小头像 */
            .avatar {
                width: 30px;
                height: 30px;
                font-size: 0.875rem;
            }
            
            /* 减小按钮间距 */
            .topbar-actions .nav-item {
                margin-left: 0.5rem !important;
            }
            
            /* 移除下拉箭头以节省空间 */
            .topbar-actions .dropdown-toggle::after {
                display: none !important;
            }
            
            /* 缩小汉堡菜单按钮 */
            .topbar-toggler {
                font-size: 1.25rem;
                padding: 0.25rem 0.5rem;
                margin-right: 0.5rem;
            }
            .overview-card .content .title {
                font-size: 1rem;
            }
            .overview-card .content .subtitle {
                font-size: 0.75rem;
            }
            
            /* 超小屏幕表格滚动 */
            .table-responsive {
                -webkit-overflow-scrolling: touch;
            }
            
            /* 超小屏幕按钮 */
            .btn-sm {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
            }
            
            /* 超小屏幕订阅卡片 */
            #pricing-container .card {
                margin-bottom: 1rem;
            }
            #pricing-container .card-title.pricing-card-title {
                font-size: 2rem;
            }
            
            /* 超小屏幕设备管理 */
            .device-item {
                font-size: 0.875rem;
            }
            .device-item .badge {
                font-size: 0.7rem;
            }
            
            /* 超小屏幕订阅信息优化 */
            .subscription-info {
                padding: 0.75rem;
                font-size: 0.875rem;
            }
            
            /* 超小屏幕标题优化 */
            h1.h2 {
                font-size: 1.25rem;
                margin-bottom: 1rem;
            }
            
            /* 超小屏幕登录按钮 */
            #direct-login-btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }
            
            /* 超小屏幕提示列表 */
            .alert ol, .alert ul {
                padding-left: 1.25rem;
                margin-bottom: 0.5rem;
            }
            
            /* 超小屏幕使用指南 */
            .card-body ol {
                padding-left: 1.25rem;
            }
            .card-body ol li {
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
            }
            
            /* 针对iPhone SE等超小屏幕的额外优化 */
            @media (max-width: 375px) {
                .topbar {
                    padding: 0 0.5rem;
                }
                
                .topbar-title {
                    font-size: 0.9rem;
                    margin: 0 0.25rem;
                }
                
                .topbar-toggler {
                    font-size: 1.1rem;
                    padding: 0.2rem 0.4rem;
                    margin-right: 0.25rem;
                }
                
                .topbar-actions .nav-item {
                    margin-left: 0.25rem !important;
                }
                
                .avatar {
                    width: 28px;
                    height: 28px;
                    font-size: 0.8rem;
                }
                
                .bi-translate {
                    font-size: 1rem;
                }
            }
            
        }

        /* Sidebar toggler button */
        .topbar-toggler {
            display: none; /* Hidden by default */
            border: none;
            background: none;
            font-size: 1.5rem;
            margin-right: 1rem;
            color: #6c757d;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            transition: all 0.2s;
        }
        .topbar-toggler:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        .topbar-toggler:active {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* Loading Overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.85);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            flex-direction: column;
            backdrop-filter: blur(3px);
        }
        .loading-overlay p {
            margin-top: 1rem;
            font-weight: 500;
            color: var(--primary-color);
        }
        .loading-overlay .spinner-border {
            color: var(--accent-color);
        }
        
        /* 侧边栏遮罩层 */
        .sidebar-backdrop {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1035;
        }
        
        @media (max-width: 767.98px) {
            .sidebar-backdrop.show {
                display: block;
            }
            
            /* 确保侧边栏在手机端正确显示 */
            .sidebar {
                position: fixed;
                height: 100vh;
                overflow-y: auto;
            }
        }
        
        /* 移动端触摸优化 */
        @media (hover: none) and (pointer: coarse) {
            .nav-link, .btn, .dropdown-item {
                -webkit-tap-highlight-color: rgba(52, 152, 219, 0.2);
            }
            
            .nav-link:active, .btn:active {
                opacity: 0.8;
            }
            
            /* 增大触摸目标区域 */
            .nav-link {
                min-height: 44px;
                display: flex;
                align-items: center;
            }
            
            .dropdown-item {
                padding: 0.6rem 1rem;
            }
        }
        
        /* 表格响应式样式 */
        @media (max-width: 767.98px) {
            .table-mobile-responsive {
                display: block;
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }
            
            .table-mobile-responsive table {
                min-width: 500px;
            }
            
            /* 移动端表格卡片化显示 */
            .table-mobile-cards tbody tr {
                display: block;
                margin-bottom: 1rem;
                border: 1px solid #dee2e6;
                border-radius: 0.5rem;
                padding: 0.75rem;
                background: #fff;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            
            .table-mobile-cards tbody td {
                display: block;
                text-align: right;
                padding: 0.25rem 0;
                border: none;
            }
            
            .table-mobile-cards tbody td::before {
                content: attr(data-label);
                float: left;
                font-weight: bold;
                text-transform: uppercase;
                font-size: 0.75rem;
                color: #6c757d;
            }
            
            .table-mobile-cards thead {
                display: none;
            }
        }

    </style>
</head>
<body class="zhCN">
    <div class="layout-container">
        <!-- Sidebar Backdrop -->
        <div class="sidebar-backdrop" id="sidebarBackdrop"></div>
        
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebarMenu">
            <a href="#" class="sidebar-logo">
                <img src="static/images/logo.png" alt="Logo"> <!-- Optional: Use your own logo -->
                <span data-i18n="page.title.short">ChatGPTPro俱乐部</span>
            </a>
            <div class="sidebar-menu">
                <div class="menu-group">
                    <ul class="nav flex-column">
                    <li class="nav-item">
                            <a class="nav-link active" href="#" data-section="dashboard">
                                <i class="bi bi-grid-1x2-fill"></i> <span data-i18n="nav.dashboard">控制面板</span>
                            </a>
                    </li>
                    <li class="nav-item">
                            <a class="nav-link" href="#" data-section="usage-guide">
                                <i class="bi bi-book"></i> <span data-i18n="nav.usage_guide">使用指南</span>
                            </a>
                    </li>
                </ul>
                </div>
                <div class="menu-group">
                    <div class="group-title" data-i18n="nav.subscription">订阅</div>
                    <ul class="nav flex-column">
                       <li class="nav-item">
                            <a class="nav-link" href="#" data-section="my-subscription">
                                <i class="bi bi-credit-card-2-front"></i> <span data-i18n="nav.my_subscription">我的订阅</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-section="pricing">
                                <i class="bi bi-cart3"></i> <span data-i18n="nav.pricing">购买/续费</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="menu-group">
                    <div class="group-title" data-i18n="nav.management">管理</div>
                    <ul class="nav flex-column">
                       <li class="nav-item">
                            <a class="nav-link" href="#" data-section="device-management">
                                <i class="bi bi-hdd-stack"></i> <span data-i18n="nav.device_management">设备管理</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="sidebar-social">
                <div class="social-header" data-i18n="nav.social_links">社区链接</div>
                <div class="social-links">
                    <a href="https://t.me/gptproclub" target="_blank" rel="noopener noreferrer" data-i18n-title="nav.tg_group" class="social-link">
                        <i class="bi bi-telegram"></i>
                        <span data-i18n="nav.tg_group">TG群</span>
                    </a>
                    <a href="https://t.me/chatgptpro_notification" target="_blank" rel="noopener noreferrer" data-i18n-title="nav.tg_channel" class="social-link">
                        <i class="bi bi-broadcast"></i>
                        <span data-i18n="nav.tg_channel">TG频道</span>
                    </a>
                    <a href="https://discord.gg/d6FnJKrekQ" target="_blank" rel="noopener noreferrer" title="Discord" class="social-link">
                        <i class="bi bi-discord"></i>
                        <span>Discord</span>
                    </a>
                </div>
            </div>
            <div class="sidebar-footer">
                  <span data-i18n="page.title.short">ChatGPTPro俱乐部</span> v2.2.0
        </div>
    </nav>

        <!-- Main Content Area -->
        <div class="layout-main">
            <!-- Top Bar -->
            <header class="topbar">
                <button class="topbar-toggler" type="button" id="sidebarToggle">
                    <i class="bi bi-list"></i>
                </button>
                <span class="topbar-title" id="topbar-title" data-i18n="nav.dashboard">控制面板</span>
                <div class="topbar-actions">
                    <!-- Language Switcher -->
                    <div class="nav-item dropdown me-2">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" title="Language / 语言">
                            <i class="bi bi-translate me-1"></i>
                            <span id="current-language">中文</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                            <li><a class="dropdown-item language-option" href="#" data-lang="zh-CN">中文</a></li>
                            <li><a class="dropdown-item language-option" href="#" data-lang="en">English</a></li>
                        </ul>
                    </div>
                    <!-- User dropdown -->
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-bs-toggle="tooltip" data-bs-placement="bottom" title="">
                            <span class="avatar me-2" id="user-avatar">?</span>
                            <span id="user-display">加载中...</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li class="dropdown-header d-sm-none" id="dropdown-user-info">
                                <div class="fw-bold" id="dropdown-username">加载中...</div>
                                <small class="text-muted" id="dropdown-email">--</small>
                            </li>
                            <li class="d-sm-none"><hr class="dropdown-divider"></li>
                            <li><button class="dropdown-item" id="logout-btn"><i class="bi bi-box-arrow-right me-2"></i><span data-i18n="user.logout">退出登录</span></button></li>
                        </ul>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="main-content">

                <!-- Dashboard Section -->
                <div class="section-content" id="dashboard-section">
                    <h1 class="h2 mb-4" data-i18n="overview.title">服务概览</h1>
                    <div class="row">
                        <div class="col-md-6 col-xl-3 mb-3">
                             <div class="card overview-card">
                                <div class="card-body">
                                    <div class="icon text-primary"><i class="bi bi-calendar-check"></i></div>
                                    <div class="content">
                                        <div class="title" id="overview-expiry">--</div>
                                        <div class="subtitle" data-i18n="overview.expiry_time">订阅到期时间</div>
                    </div>
                                </div>
                            </div>
                        </div>
                         <div class="col-md-6 col-xl-3 mb-3">
                             <div class="card overview-card">
                    <div class="card-body">
                                    <div class="icon text-success"><i class="bi bi-patch-check-fill"></i></div>
                                    <div class="content">
                                        <div class="title" id="overview-status">--</div>
                                        <div class="subtitle" data-i18n="overview.subscription_status">订阅状态</div>
                        </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-xl-3 mb-3">
                             <div class="card overview-card">
                                <div class="card-body">
                                    <div class="icon text-info"><i class="bi bi-hdd-stack"></i></div>
                                    <div class="content">
                                        <div class="title" id="overview-devices">--</div>
                                        <div class="subtitle" data-i18n="overview.device_quota">设备额度</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                         <div class="col-md-6 col-xl-3 mb-3">
                             <div class="card overview-card">
                                <div class="card-body">
                                    <div class="icon text-warning"><i class="bi bi-info-circle"></i></div>
                                    <div class="content">
                                        <div class="title" id="overview-guide" data-i18n="overview.view_guide">查看指南</div>
                                        <div class="subtitle" data-i18n="overview.quick_start">快速开始</div>
                                    </div>
                                     <a href="#" class="stretched-link" data-section="usage-guide"></a>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- Device/Login Card -->
                <div class="card mb-4">
                        <div class="card-header">
                            <i class="bi bi-box-arrow-in-right"></i> <span data-i18n="adspower.login_title">AdsPower 登录</span>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                <span id="device-usage-message-content">
                                <strong data-i18n="adspower.device_usage">设备使用说明:</strong> <span data-i18n="adspower.device_usage_desc" data-i18n-args='["<strong><span id=\"device-limit-text\">2</span></strong>"]'>您的订阅最多允许同时使用 <strong><span id="device-limit-text">2</span></strong> 台设备。系统会根据负载情况自动为您分配AdsPower账号。</span>
                                </span>
                        </div>
                        <div class="alert alert-warning border-warning" style="background-color: #fff3cd; border-width: 2px;">
                            <h5 class="alert-heading"><i class="bi bi-exclamation-triangle-fill me-2"></i><span data-i18n="adspower.important_reminder">重要提醒</span></h5>
                            <p class="mb-2"><strong data-i18n="adspower.before_login">在点击"登录AdsPower"按钮前，请确保：</strong></p>
                            <ol class="mb-2">
                                <li data-i18n="adspower.requirement_1">已经 <a href="https://www.adspower.net/download" target="_blank" rel="noopener noreferrer" class="alert-link fw-bold"><span data-i18n="adspower.download_client">下载并安装AdsPower客户端</span> <i class="bi bi-box-arrow-up-right small"></i></a></li>
                                <li data-i18n="adspower.requirement_2">AdsPower客户端可以正常启动</li>
                                <li data-i18n="adspower.requirement_3">您已准备好立即进行登录操作</li>
                            </ol>
                            <hr class="my-2">
                            <p class="mb-0 small"><i class="bi bi-clock-history"></i> <span data-i18n="adspower.session_timeout">注意：登录会话有3分钟时间限制，请确保准备就绪后再点击登录按钮。</span></p>
                        </div>
                        
                            <!-- Direct Login Area -->
                        <div id="profiles-container" class="mt-3">
                                <!-- Content generated by updateUIForDirectLogin() -->
                                <div class="text-center my-3">
                                    <div class="spinner-border text-secondary" role="status">
                                        <span class="visually-hidden" data-i18n="common.loading">加载中...</span>
                                    </div>
                                    <p class="mt-2 text-muted" data-i18n="adspower.allocating">正在加载登录信息...</p>
                                </div>
                        </div>
                        
                            <div id="profile-actions" class="mt-3">
                                <!-- Content generated by updateUIForDirectLogin() -->
                        </div>
                    </div>
                </div>
                </div><!-- /#dashboard-section -->

                <!-- My Subscription Section -->
                 <div class="section-content" id="my-subscription-section" style="display: none;">
                     <h1 class="h2 mb-4" data-i18n="subscription.title">我的订阅</h1>
                     <div class="card mb-4" id="subscription-container">
                         <div class="card-header">
                            <i class="bi bi-credit-card-fill"></i> <span data-i18n="subscription.details">订阅详情</span>
                    </div>
                    <div class="card-body">
                             <div class="subscription-info" id="subscription-detail">
                                 <!-- Subscription details will be loaded here by JS -->
                                <div class="text-center p-3">
                                     <div class="spinner-border text-primary" role="status">
                                         <span class="visually-hidden" data-i18n="subscription.loading">加载订阅信息...</span>
                        </div>
                                     <p class="mt-2" data-i18n="subscription.loading">正在加载订阅信息...</p>
                        </div>
                    </div>
                             <div class="mt-3" id="subscription-actions">
                                 <!-- Action buttons will be loaded here by JS -->
                </div>
        </div>
    </div>
                 </div><!-- /#my-subscription-section -->

                <!-- Pricing Section -->
                <div class="section-content" id="pricing-section" style="display: none;">
                     <h1 class="h2 mb-4" data-i18n="pricing.title">购买 / 续费</h1>
                     <div class="alert alert-warning mb-4" id="guest-notice" style="display: none;">
                         <i class="bi bi-exclamation-circle-fill me-2"></i>
                         <span data-i18n="pricing.guest_notice">您当前以访客身份浏览套餐。</span>
                         <a href="/login" class="alert-link ms-2">
                             <i class="bi bi-box-arrow-in-right"></i> <span data-i18n="user.login_to_subscribe">登录后订阅</span>
                         </a>
                     </div>
                     <div class="card mt-3" id="pricing-container">
                         <div class="card-header">
                             <i class="bi bi-tags-fill"></i> <span data-i18n="pricing.select_plan">选择订阅套餐</span>
                </div>
                                <div class="card-body">
                             <p class="lead" data-i18n="pricing.select_desc">选择或续费您的订阅计划。</p>
                             <div class="alert alert-info">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                <span data-i18n="pricing.features">我们的特点：基于指纹浏览器，<strong>环境安全，不降智不封号</strong>，原生官网号，全部功能都可以使用。<strong>直接获得正规官网账号访问权限！</strong></span>
                                </div>
                             <div class="row" id="subscription-plans-container">
                                 <!-- Subscription plans will be loaded here by JS -->
                                 <div class="text-center my-5">
                                     <div class="spinner-border text-primary" role="status">
                                         <span class="visually-hidden" data-i18n="common.loading">加载中...</span>
                            </div>
                                     <p class="mt-2" data-i18n="pricing.loading">正在加载订阅套餐...</p>
                        </div>
                             </div>
                         </div>
                     </div>
                 </div><!-- /#pricing-section -->


                <!-- Usage Guide Section -->
                <div class="section-content" id="usage-guide-section" style="display: none;">
                    <h1 class="h2 mb-4" data-i18n="guide.title">使用指南</h1>
                     <div class="card mb-4">
                        <div class="card-header">
                            <i class="bi bi-lightbulb-fill"></i> <span data-i18n="guide.login_steps">ChatGPT拼车账号登录步骤</span>
                        </div>
                                <div class="card-body">
                            <ol>
                                <li data-i18n="guide.step_1">点击控制面板上的 <strong><i class="bi bi-box-arrow-in-right"></i> 登录AdsPower</strong> 按钮获取登录凭证。</li>
                                <li data-i18n="guide.step_2">在弹出的新页面中，您将看到AdsPower账号的用户名、密码和验证码 (若需要)。</li>
                                <li data-i18n="guide.step_3"><strong>下载并安装AdsPower客户端</strong>（<a href="https://www.adspower.com/download" target="_blank">官方下载 <i class="bi bi-box-arrow-up-right"></i></a>）。强烈建议使用客户端，不要使用网页版。</li>
                                <li data-i18n="guide.step_4">使用我们提供的用户名、密码和验证码登录AdsPower客户端。</li>
                                <li data-i18n="guide.step_5"><strong>国内用户</strong>请选择 "国内环境" 登录。</li>
                                <li data-i18n="guide.step_6"><strong>海外用户</strong>请选择 "directus环境" 或类似选项登录。</li>
                                <li data-i18n="guide.step_7">登录成功后，您可以在AdsPower提供的浏览器中使用ChatGPT及所有Pro功能。</li>
                            </ol>
                            <div class="alert alert-warning mt-4">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <strong data-i18n="guide.important_tips">重要提示：</strong>
                                <ul>
                                    <li data-i18n="guide.tip_1">请勿修改AdsPower账号的任何设置（特别是密码或邮箱），这可能导致您的订阅失效且无法恢复。</li>
                                    <li data-i18n="guide.tip_2">请勿在AdsPower内安装非必要的浏览器扩展。</li>
                                    <li data-i18n="guide.tip_3">同一时间只允许一个用户登录同一个分配到的AdsPower账号。</li>
                                    <li data-i18n="guide.tip_4">您的订阅包含设备数量限制，请勿超额使用。</li>
                                    </ul>
                                </div>
                        </div>
                    </div>
                </div><!-- /#usage-guide-section -->

                <!-- Device Management Section -->
                <div class="section-content" id="device-management-section" style="display: none;">
                    <h1 class="h2 mb-4" data-i18n="device.title">设备管理</h1>

                    <!-- Device Overview Stats -->
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="card-title text-primary mb-1"><i class="bi bi-hdd-stack me-1"></i><span data-i18n="device.quota">设备额度</span></h5>
                                        <button class="btn btn-sm btn-outline-primary" onclick="refreshDeviceQuota()" data-i18n-title="device.refresh">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                    </div>
                                    <p class="card-text fs-4 fw-bold" id="device-quota-stats">-- / --</p>
                                    <div class="progress" style="height: 10px;" id="device-quota-progress-bar-container" >
                                        <div id="device-quota-progress-bar" class="progress-bar bg-success" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <small class="text-muted mt-1 d-block" id="device-quota-message" data-i18n="device.quota_good">管理您的设备以优化使用。</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                     <h5 class="card-title text-info mb-2"><i class="bi bi-lightbulb me-1"></i><span data-i18n="device.operation_tips">操作提示</span></h5>
                                     <p class="card-text small" data-i18n="device.tips_content">
                                         <span data-i18n="device.tips_content">您可以点击设备列表右侧的 <button class="btn btn-outline-danger btn-sm py-0 disabled"><i class="bi bi-box-arrow-right"></i> <span data-i18n="device.logout">登出</span></button> 按钮来移除不再使用的设备，从而释放您的设备额度。</span>
                                     </p>
                                     <p class="card-text small mb-0" data-i18n="device.tips_content2">
                                         定期检查并清理不活跃的设备，确保您的账户安全和额度充足。
                                     </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Device Overview Stats -->

                    <div class="card">
                        <div class="card-header">
                            <i class="bi bi-hdd-network-fill"></i> <span data-i18n="device.logged_devices">已登录设备列表</span>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-secondary">
                                <i class="bi bi-info-circle me-1"></i> <span data-i18n="device.list_desc">这里列出了您当前账户下所有已记录的设备。您可以登出不再使用的设备以释放额度。</span>
                            </div>
                            <div class="table-responsive table-mobile-responsive">
                                <table class="table table-hover align-middle table-mobile-cards">
                                    <thead>
                                        <tr>
                                            <th data-i18n="device.name">设备名称</th>
                                            <th data-i18n="device.ip">IP 地址</th>
                                            <th data-i18n="device.type">设备类型</th>
                                            <th data-i18n="device.operation">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="devices-table-body">
                                        <!-- Device rows will be loaded here -->
                                    </tbody>
                                </table>
                                <div id="no-devices-message" class="alert alert-light text-center" style="display: none;" data-i18n="device.no_devices">
                                    当前没有已记录的设备。
                                </div>
                            </div>
                        </div>
                    </div>
                </div><!-- /#device-management-section -->

            </main>
        </div><!-- / .layout-main -->
    </div><!-- / .layout-container -->

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
         <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
             <span class="visually-hidden" data-i18n="common.processing">处理中...</span>
        </div>
         <p id="loading-message" data-i18n="common.processing">请稍候...</p>
    </div>

    <script src="https://gcore.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 国际化支持 -->
    <script src="{{ url_for('static', filename='js/i18n.js') }}"></script>
    <!-- 通用工具函数库 -->
    <script src="{{ url_for('static', filename='js/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>

    <!-- Chatwoot 客服支持脚本 -->
    <script>
      // 获取当前登录用户信息
      var user = JSON.parse(localStorage.getItem('user') || '{}');
      var token = localStorage.getItem('token');
      
      // 获取当前页面语言设置
      var currentLang = localStorage.getItem('language') || 'zh-CN';
      
      // 映射到 Chatwoot 支持的语言代码
      var chatwootLangMap = {
        'zh-CN': 'zh_CN',
        'en': 'en'
      };
      var chatwootLocale = chatwootLangMap[currentLang] || 'zh_CN';
      
      // 设置全局 Chatwoot 设置（必须在脚本加载前设置）
      window.chatwootSettings = {
        hideMessageBubble: false,
        position: 'right',
        locale: chatwootLocale,
        type: 'standard',
        launcherTitle: ''  // 设置为空字符串，不显示标题
      };
      
      // 如果用户已登录，设置用户信息
      if (user && user.id && token) {
        window.chatwootSettings.user = {
          identifier: user.email,
          email: user.email,
          name: user.username || user.email.split('@')[0],
          identifier_hash: '',  // 如果需要验证，可以在后端生成 HMAC
          custom_attributes: {
            user_id: user.id,
            is_admin: user.is_admin || false,
            user_type: 'registered',
            platform: 'web',
            language: currentLang
          }
        };
      }
      
      // 加载 Chatwoot SDK
      (function(d,t) {
        var BASE_URL="https://support.chatgptpro.club";
        var g=d.createElement(t),s=d.getElementsByTagName(t)[0];
        g.src=BASE_URL+"/packs/js/sdk.js";
        g.defer = true;
        g.async = true;
        s.parentNode.insertBefore(g,s);
        g.onload=function(){
          window.chatwootSDK.run({
            websiteToken: 'rCAWY44bYvpjZm14wvdAUfzc',
            baseUrl: BASE_URL
          });
          
          // SDK 加载完成后，如果需要手动设置用户
          setTimeout(function() {
            if (window.$chatwoot && user && user.id) {
              // 尝试使用 setUser 方法
              if (window.$chatwoot.setUser) {
                window.$chatwoot.setUser(user.email, {
                  email: user.email,
                  name: user.username || user.email.split('@')[0],
                  identifier_hash: ''
                });
                
                // 设置自定义属性
                if (window.$chatwoot.setCustomAttributes) {
                  window.$chatwoot.setCustomAttributes({
                    user_id: user.id,
                    is_admin: user.is_admin || false,
                    user_type: 'registered',
                    platform: 'web',
                    language: currentLang
                  });
                }
              }
            }
          }, 1000); // 延迟1秒确保SDK完全初始化
        }
      })(document,"script");
      
      // 监听语言切换事件
      document.addEventListener('languageChanged', function(e) {
        var newLang = e.detail.language;
        var newChatwootLocale = chatwootLangMap[newLang] || 'zh_CN';
        
        if (window.$chatwoot) {
          if (window.$chatwoot.setLocale) {
            window.$chatwoot.setLocale(newChatwootLocale);
          }
          
          if (user && user.id && window.$chatwoot.setCustomAttributes) {
            window.$chatwoot.setCustomAttributes({
              language: newLang
            });
          }
        }
      });
    </script>

</body>
</html> 