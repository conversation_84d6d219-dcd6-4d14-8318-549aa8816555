<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ChatGPTPro俱乐部 - 尊贵AI拼车体验，多级拼车服务，专业技术支持，为您节省90%使用成本">
    <title>ChatGPTPro俱乐部 - 尊享AI拼车服务</title>
    <link rel="icon" href="static/images/logo.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        :root {
            /* 现代化渐变配色方案 - 与dashboard保持一致 */
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --accent-color: #764ba2;
            --accent-light: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --danger-color: #f56565;
            --info-color: #4299e1;

            /* 背景渐变 */
            --body-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --hero-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-bg: rgba(255, 255, 255, 0.95);
            --navbar-bg: rgba(255, 255, 255, 0.95);

            /* 现代化阴影系统 */
            --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
            --navbar-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --text-muted: #718096;
            --border-color: rgba(102, 126, 234, 0.1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: var(--body-bg);
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        .navbar {
            background: var(--navbar-bg);
            backdrop-filter: blur(20px);
            box-shadow: var(--navbar-shadow);
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .navbar-brand img {
            filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
            transition: all 0.3s ease;
        }

        .navbar-brand:hover img {
            transform: scale(1.05);
        }

        .hero-section {
            background: var(--hero-bg);
            color: white;
            padding: 120px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: heroFloat 15s ease-in-out infinite;
        }

        @keyframes heroFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .hero-section .container {
            position: relative;
            z-index: 1;
        }

        .hero-section h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 1s ease-out;
        }

        .hero-section .lead {
            font-size: 1.4rem;
            font-weight: 400;
            margin-bottom: 2.5rem;
            opacity: 0.95;
            animation: fadeInUp 1s ease-out 0.2s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 现代化按钮设计 */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            animation: fadeInUp 1s ease-out 0.4s both;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--accent-color) 100%);
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
        }

        /* 特性图标现代化 */
        .feature-icon {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            padding: 1.5rem;
            border-radius: 20px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            display: inline-block;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-icon:hover::before {
            opacity: 1;
        }

        .feature-icon:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        /* 定价卡片现代化 */
        .pricing-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
            overflow: hidden;
            position: relative;
        }

        .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .pricing-card:hover::before {
            opacity: 1;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--card-shadow-hover);
            border-color: rgba(102, 126, 234, 0.2);
        }

        .pricing-card .card-header {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .pricing-card .card-body {
            padding: 2rem 1.5rem;
            position: relative;
            z-index: 1;
        }

        .pricing-card h2 {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }
        
        /* 现代化加载动画 */
        .loading-spinner {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 16px;
            box-shadow: var(--card-shadow);
        }

        .spinner-border {
            border-width: 3px;
            border-color: var(--primary-color) transparent var(--primary-color) transparent;
            animation: spin 1s linear infinite, pulse-border 2s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse-border {
            0%, 100% { border-width: 3px; }
            50% { border-width: 4px; }
        }

        /* 推荐卡片现代化 */
        .testimonial-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .testimonial-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .testimonial-card:hover::before {
            opacity: 1;
        }

        .testimonial-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--card-shadow-hover);
            border-color: rgba(102, 126, 234, 0.2);
        }

        .testimonial-card .bi-quote {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* 社交媒体按钮现代化 */
        .community-btn {
            background: rgba(102, 126, 234, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(102, 126, 234, 0.3);
            color: var(--primary-color);
            font-weight: 600;
            border-radius: 10px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0.5rem 1rem;
            position: relative;
            overflow: hidden;
        }

        .community-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .community-btn:hover::before {
            left: 100%;
        }

        .community-btn:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .community-btn i {
            font-size: 1.2rem;
            margin-right: 0.5rem;
        }

        .community-btn.telegram:hover {
            background: linear-gradient(135deg, #0088cc 0%, #0077b3 100%);
            box-shadow: 0 8px 25px rgba(0, 136, 204, 0.3);
        }

        .community-btn.discord:hover {
            background: linear-gradient(135deg, #5865F2 0%, #4752C4 100%);
            box-shadow: 0 8px 25px rgba(88, 101, 242, 0.3);
        }

        /* 区域样式现代化 */
        .py-5 {
            padding: 4rem 0;
        }

        .bg-light {
            background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
            backdrop-filter: blur(10px);
        }

        /* 页脚现代化 */
        footer {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%) !important;
            position: relative;
            overflow: hidden;
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
        }

        footer .container {
            position: relative;
            z-index: 1;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .hero-section h1 {
                font-size: 2.5rem;
            }

            .hero-section .lead {
                font-size: 1.2rem;
            }

            .btn-primary, .btn-outline-light {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
                margin-bottom: 1rem;
            }

            .feature-icon {
                font-size: 2.5rem;
                padding: 1rem;
            }

            .community-btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Spinner -->
    <div class="loading-spinner">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="static/images/logo.png" alt="Logo" height="30" class="d-inline-block align-text-top">
                ChatGPTPro俱乐部
            </a>
            <div class="navbar-nav ms-auto d-flex align-items-center">
                <!-- Community Links -->
                <div class="d-none d-md-flex me-3 gap-2">
                    <a href="https://t.me/gptproclub" target="_blank" rel="noopener noreferrer" class="btn community-btn telegram d-flex align-items-center">
                        <i class="bi bi-telegram me-1"></i> TG群
                    </a>
                    <a href="https://t.me/chatgptpro_notification" target="_blank" rel="noopener noreferrer" class="btn community-btn telegram d-flex align-items-center">
                        <i class="bi bi-broadcast me-1"></i> TG频道
                    </a>
                    <a href="https://discord.gg/d6FnJKrekQ" target="_blank" rel="noopener noreferrer" class="btn community-btn discord d-flex align-items-center">
                        <i class="bi bi-discord me-1"></i> Discord
                    </a>
                </div>
                <a class="btn btn-primary" href="/login" id="login-btn">
                    <i class="bi bi-box-arrow-in-right"></i> 登录
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">欢迎来到 ChatGPTPro俱乐部</h1>
            <p class="lead mb-5">尊贵AI助手拼车体验，为您节省90%使用成本</p>
            <a href="#pricing" class="btn btn-light btn-lg me-3">
                <i class="bi bi-cart3"></i> 查看套餐
            </a>
            <a href="/login" class="btn btn-outline-light btn-lg">
                <i class="bi bi-box-arrow-in-right"></i> 立即登录
            </a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">为什么选择我们</h2>
            <div class="row">
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon text-primary">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <h4>安全可靠</h4>
                    <p class="text-muted">基于指纹浏览器技术，环境安全隔离，不降智不封号，原生官网账号</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon text-success">
                        <i class="bi bi-people"></i>
                    </div>
                    <h4>灵活选择</h4>
                    <p class="text-muted">1人独享车、5人车、10人车等多种套餐，满足不同需求</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon text-info">
                        <i class="bi bi-headset"></i>
                    </div>
                    <h4>专业服务</h4>
                    <p class="text-muted">多位成员提供支持，快速响应，专业技术团队保障服务质量</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-5 bg-light" id="pricing">
        <div class="container">
            <h2 class="text-center mb-5">选择您的套餐</h2>
            <div class="row" id="pricing-cards">
                <!-- Pricing cards will be loaded here -->
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载套餐信息...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonial Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card testimonial-card shadow-sm">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-quote text-primary" style="font-size: 2rem; line-height: 1;"></i>
                                <div class="ms-3">
                                    <p class="mb-3" style="font-style: italic;">
                                        加入5人共享后，GPT-4o生成的参考图帮我突破了设计瓶颈，素材准备时间从2小时缩短到20分钟。图像质量好到让客户惊讶，整个设计流程更加流畅高效。
                                    </p>
                                    <p class="mb-0 text-end">
                                        <strong>— 陈总</strong>
                                        <span class="text-muted">，创意公司合作伙伴</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-5 text-center">
        <div class="container">
            <h3 class="mb-4">准备好开始了吗？</h3>
            <p class="lead mb-4">加入200+尊贵会员，享受高品质AI服务</p>
            <a href="/login" class="btn btn-primary btn-lg">
                <i class="bi bi-box-arrow-in-right"></i> 立即加入
            </a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-4 bg-dark text-white text-center">
        <div class="container">
            <p class="mb-0">&copy; 2025 ChatGPTPro俱乐部. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check if user is logged in and redirect accordingly
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            
            if (token && user.id) {
                // User is logged in, redirect to appropriate page
                document.querySelector('.loading-spinner').style.display = 'block';
                if (user.is_admin) {
                    window.location.href = '/admin';
                } else {
                    window.location.href = '/dashboard';
                }
                return;
            }
            
            // Load pricing plans
            loadPricingPlans();
        });
        
        function loadPricingPlans() {
            fetch('/api/public/subscription-types')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('pricing-cards');
                    
                    if (data.success && data.data && data.data.types && data.data.types.length > 0) {
                        container.innerHTML = '';
                        
                        data.data.types.forEach((type, index) => {
                            const col = document.createElement('div');
                            col.className = 'col-lg-4 col-md-6 mb-4';

                            // 添加推荐标签逻辑（为中等价格的套餐添加推荐标签）
                            const isRecommended = data.data.types.length > 1 &&
                                                index === Math.floor(data.data.types.length / 2);

                            col.innerHTML = `
                                <div class="card pricing-card position-relative">
                                    ${isRecommended ? `
                                        <div class="position-absolute top-0 start-50 translate-middle" style="z-index: 10;">
                                            <span class="badge text-white px-3 py-2" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 20px; font-size: 0.8rem; font-weight: 600; box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);">
                                                <i class="bi bi-star-fill me-1"></i>推荐
                                            </span>
                                        </div>
                                    ` : ''}
                                    <div class="card-header text-center">
                                        <h4>${type.name}</h4>
                                    </div>
                                    <div class="card-body">
                                        <h2 class="text-center mb-4">¥${type.price.toFixed(2)}<small class="text-muted">/${type.days}天</small></h2>
                                        <ul class="list-unstyled">
                                            <li class="mb-2"><i class="bi bi-check-circle text-success"></i> 最多${type.max_devices}台设备</li>
                                            ${type.requirements ? type.requirements.split('\n').filter(line => line.trim()).map(line =>
                                                `<li class="mb-2"><i class="bi bi-check-circle text-success"></i> ${line.trim()}</li>`
                                            ).join('') : ''}
                                        </ul>
                                        <div class="text-center mt-4">
                                            <a href="/login" class="btn btn-outline-primary w-100" style="position: relative; z-index: 5;">
                                                <i class="bi bi-box-arrow-in-right"></i> 登录后订阅
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            `;

                            container.appendChild(col);
                        });
                    } else {
                        container.innerHTML = '<div class="col-12 text-center"><p>暂无可用套餐</p></div>';
                    }
                })
                .catch(error => {
                    console.error('Failed to load pricing:', error);
                    document.getElementById('pricing-cards').innerHTML = 
                        '<div class="col-12 text-center"><p class="text-danger">加载套餐信息失败，请稍后重试</p></div>';
                });
        }
    </script>
</body>
</html>